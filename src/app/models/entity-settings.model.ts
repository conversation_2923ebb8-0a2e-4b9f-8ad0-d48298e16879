export type AvailableTwoFAOptions = 'sms' | 'email' | 'google';

export interface MailTemplate {
  from: string;
  subject: string;
  html: string;
  options?: Object;
}

export interface MailTemplatesList {
  [lang: string]: MailTemplate;
}

export interface SmsTemplatesList {
  [lang: string]: string;
}

export interface TwoFASettings {
  isAuthEnabled: boolean;
  authOptions: AvailableTwoFAOptions[];
  mailTemplates: MailTemplatesList;
  smsTemplates: SmsTemplatesList;
}

export interface EntitySettings {
  biLocal?: boolean;
  twoFactorAuthSettings?: TwoFASettings;
}
