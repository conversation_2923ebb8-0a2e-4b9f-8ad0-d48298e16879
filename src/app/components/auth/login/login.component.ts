import { Component, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { catchError, tap } from 'rxjs/operators';
import { EMPTY } from 'rxjs';

import { LoginInfo, UserCredentials } from '../../../models/user.model';
import { AuthService } from '../../../services/auth/auth.service';
import { SwHubConfigService } from '@skywind-group/lib-swui';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'sw-hub-base-login',
  encapsulation: ViewEncapsulation.None,
  styleUrls: ['login.component.scss'],
  templateUrl: 'login.component.html',
})
export class LoginComponent {
  readonly form: FormGroup;
  readonly logo: string;

  submitted = false;
  errorMsg: string;

  constructor(
    fb: FormBuilder,
    private readonly auth: AuthService,
    private readonly router: Router,
    { logo }: SwHubConfigService
  ) {
    this.logo = logo?.solo ?? 'img/logo-skywind-solo.png';
    this.form = fb.group({
      username: ['', Validators.compose([Validators.required, Validators.minLength(4)])],
      password: ['', Validators.compose([Validators.required, Validators.minLength(4)])]
    });
  }

  get userNameControl(): FormControl {
    return this.form.get('username') as FormControl;
  }

  get passwordControl(): FormControl {
    return this.form.get('password') as FormControl;
  }

  onSubmit( data: { username: string, password: string } ): void {
    this.submitted = true;
    if (this.form.valid) {
      this.errorMsg = '';
      this.auth.passwordChangeName = data.username;
      this.auth.login(data as UserCredentials).pipe(
        tap(( res: LoginInfo ) => {
          if (this.auth.twoFactorRequired()) {
            this.router.navigate(['auth/login/two-factor']);
          } else {
            this.auth.authorize(res);
          }
        }),
        catchError((errorResponse: HttpErrorResponse ) => {
          if (this.auth.twoFactorSetupRequired()) {
            this.router.navigate(['auth/login/two-factor/setup']);
          } else if (this.auth.passwordChangeRequired()) {
            this.router.navigate(['auth/changepassword']);
          } else {
            this.errorMsg = errorResponse.error.message;
          }
          return EMPTY;
        })
      ).subscribe();
    }
  }
}
