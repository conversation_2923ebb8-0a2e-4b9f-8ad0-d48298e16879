import { FormGroup } from '@angular/forms';

import { BaseApiObject } from '../typings';

export interface MailTemplate {
  from: string;
  subject: string;
  html: string;
  options?: Object;
}

export interface MailTemplatesList {
  [lang: string]: MailTemplate;
}

export interface SmsTemplatesList {
  [lang: string]: string;
}

export interface ProcessedSmsTemplatesList {
  [lang: string]: ProcessedSmsTemplate;
}

export interface ProcessedSmsTemplate {
  textMessage: string;
}

export type AvailableTwoFAOptions = 'sms' | 'email' | 'google';

export interface TwoFASettings {
  isAuthEnabled: boolean;
  authOptions: AvailableTwoFAOptions[];
  mailTemplates: MailTemplatesList;
  smsTemplates: SmsTemplatesList;
}

export function createDefaultTwoFAEmailTemplate(): MailTemplate {
  return {
    from: 'Skywind',
    subject: 'Login code',
    html: 'Hello {{username}}, here is your auth code: <b>{{authCode}}</b>',
  };
}

export function createDefaultTwoFASmsTemplate() {
  return '{{username}}, here is your sms <b>{{authCode}}</b> auth code';
}

export function createDefaultTwoFASettings(): TwoFASettings {
  return {
    isAuthEnabled: false,
    authOptions: [],
    mailTemplates: {
      default: createDefaultTwoFAEmailTemplate(),
    },
    smsTemplates: {
      default: createDefaultTwoFASmsTemplate(),
    }
  };
}

export class VirtualCurrencyRates {
  vircode: string;
  rates: VirtualCurrencyRatesItem[];

  constructor( { vircode, rates } ) {
    this.vircode = vircode;
    this.rates = rates;
  }
}

export class VirtualCurrencyRatesItem {
  curcode: string;
  value: number;

  constructor( { curcode, value } ) {
    this.curcode = curcode;
    this.value = value;
  }
}

export class VirtualCurrencyRateSettings {
  [vircode: string]: VirtualCurrencyRateSettingsItem;
}

export class VirtualCurrencyRateSettingsItem {
  [curcode: string]: number;
}

export const getRates = ( settingsItem: VirtualCurrencyRateSettingsItem,
                          entityCurrencies?: string[], ignoreEntityCurrencies: boolean = false
): VirtualCurrencyRatesItem[] => {
  return Object.keys(settingsItem).reduce(( rates, curcode ) => {
    const curcodeFoundInEntity = entityCurrencies && entityCurrencies.indexOf(curcode) > -1;
    const curcodeCanBeAdded = curcodeFoundInEntity || ignoreEntityCurrencies;
    const filterNotRequired = !entityCurrencies;
    let result = [...rates];

    if (curcodeCanBeAdded || filterNotRequired) {
      const item = new VirtualCurrencyRatesItem({ curcode, value: settingsItem[curcode] });
      result = [...rates, item];
    }

    return result;
  }, []);
};

export const getVirtualCurrencies = ( settings: VirtualCurrencyRateSettings,
                                      entityCurrencies?: string[], ignoreEntityCurrencies: boolean = false
): VirtualCurrencyRates[] => {
  return Object.keys(settings).reduce(( currencies, vircode ) => {
    const rates = new VirtualCurrencyRates({
      vircode,
      rates: getRates(settings[vircode], entityCurrencies, ignoreEntityCurrencies)
    });
    return [...currencies, rates];
  }, []);
};

export const handleFormUseDefault = ( form: FormGroup ): void => {
  Object.keys(form.controls).map(key => {
    const group = <FormGroup>form.get(key);
    group.get('useDefault').valueChanges.subscribe(value => {
      Object.keys(group.controls).map(controlKey => {
        if (controlKey !== 'useDefault') {
          value ? group.get(controlKey).disable({ emitEvent: false }) :
            group.get(controlKey).enable({ emitEvent: false });
        }
      });
    });
  });
};

export const processTemplate = ( template ) => {
  Object.keys(template).forEach(templateKey => {
    const group = template[templateKey];
    if ('useDefault' in group && group['useDefault'] === true) {
      delete template[templateKey];
    } else if ('useDefault' in group) {
      delete template[templateKey]['useDefault'];
    }
  });
  return template;
};

export const TWOFA_AVAILABLE_LANGUAGES = [
  { key: 'default', title: 'Default' },
  { key: 'en', title: 'English' },
  { key: 'zh', title: 'Chinese' }
];

export const TWOFA_TYPE_STATES_TRANSLATE = [
  'ENTITY_SETUP.TWOFA_SETUP.typeStateEnabled',
  'ENTITY_SETUP.TWOFA_SETUP.typeStateDisabled',
];

interface EmailTemplate {
  from: string;
  html: string;
  subject: string;
  options?: {
    resetBaseUrl: string;
  };
}

export interface EntityEmailTemplates {
  passwordRecovery: EmailTemplate;
  changeEmail: EmailTemplate;
}

export class EntitySettingsModel implements BaseApiObject {

  _meta?: Object;
  twoFactorAuthSettings: TwoFASettings;
  emailTemplates?: EntityEmailTemplates;
  maintenanceUrl: string;
  useSiteAuthorization: boolean;
  virtualCurrencyRate: VirtualCurrencyRateSettings;
  maxPaymentRetryAttempts: number;
  minPaymentRetryTimeout: number;
  storePlayerInfo: boolean;
  dynamicRoutingEnabled: boolean;
  isPlayerPasswordChangeEnabled: boolean;
  defaultGameGroup?: string;
  omitBnsBalance?: boolean;
  allowOverrideStakeAll?: boolean;
  finalizationSupport?: string;
  restrictedCountries?: string[];
  hideBalanceBeforeAndAfter?: boolean;
  useCountriesFromJurisdiction: boolean;
  supportedBonusPaymentMethod: 'none' | 'credit' | 'bonus';
  bonusPaymentMethod: 'manual' | 'credit' | 'bonus';
  markPlayersAsTest?: boolean;
  autoCreateTestJackpot?: boolean;
  maxTestPlayers?: number;
  flatReportsEnabled?: boolean;
  playerPrefixEnabled?: boolean;
  playerPrefix?: string;
  allowedStaticDomainsForChildId?: string[];

  // Marks entities that moved to Softgate BI; when true, only Analytics hub should be accessible
  biLocal?: boolean;

  private rawSettings: Object;

  constructor( obj?: any ) {
    this.twoFactorAuthSettings = obj && obj.twoFactorAuthSettings;

    if (obj.hasOwnProperty('maintenanceUrl')) {
      this.maintenanceUrl = obj.maintenanceUrl;
    }

    if (obj.hasOwnProperty('emailTemplates')) {
      this.emailTemplates = obj.emailTemplates;
    }

    if (obj.hasOwnProperty('useSiteAuthorization')) {
      this.useSiteAuthorization = obj.useSiteAuthorization;
    }

    if (obj.hasOwnProperty('virtualCurrencyRate')) {
      this.virtualCurrencyRate = obj.virtualCurrencyRate;
    }

    if (obj.hasOwnProperty('maxPaymentRetryAttempts')) {
      this.maxPaymentRetryAttempts = obj.maxPaymentRetryAttempts;
    }

    if (obj.hasOwnProperty('maxPaymentRetryAttempts')) {
      this.minPaymentRetryTimeout = obj.minPaymentRetryTimeout;
    }

    if (obj.hasOwnProperty('omitBnsBalance')) {
      this.omitBnsBalance = obj.omitBnsBalance;
    }

    if (obj.hasOwnProperty('allowOverrideStakeAll')) {
      this.allowOverrideStakeAll = obj.allowOverrideStakeAll;
    }

    if (obj.hasOwnProperty('restrictedCountries')) {
      this.restrictedCountries = obj.restrictedCountries;
    }

    if (obj.hasOwnProperty('hideBalanceBeforeAndAfter')) {
      this.hideBalanceBeforeAndAfter = obj.hideBalanceBeforeAndAfter;
    }

    if (obj.hasOwnProperty('useCountriesFromJurisdiction')) {
      this.useCountriesFromJurisdiction = obj.useCountriesFromJurisdiction;
    }

    if (obj.hasOwnProperty('supportedBonusPaymentMethod')) {
      this.supportedBonusPaymentMethod = obj.supportedBonusPaymentMethod;
    }

    if (obj.hasOwnProperty('bonusPaymentMethod')) {
      this.bonusPaymentMethod = obj.bonusPaymentMethod;
    }

    if (obj.hasOwnProperty('markPlayersAsTest')) {
      this.markPlayersAsTest = obj.markPlayersAsTest;
    }

    if (obj.hasOwnProperty('autoCreateTestJackpot')) {
      this.autoCreateTestJackpot = obj.autoCreateTestJackpot;
    }

    if (obj.hasOwnProperty('playerPrefixEnabled')) {
      this.playerPrefixEnabled = obj.playerPrefixEnabled;
    }

    if (obj.hasOwnProperty('maxTestPlayers')) {
      this.maxTestPlayers = obj.maxTestPlayers;
    }

    if (obj.hasOwnProperty('flatReportsEnabled')) {
      this.flatReportsEnabled = obj.flatReportsEnabled;
    }

    if (obj.hasOwnProperty('allowedStaticDomainsForChildId')) {
      this.allowedStaticDomainsForChildId = obj.allowedStaticDomainsForChildId;
    }

    this.check2FASettingsIntegrity();

    this.rawSettings = obj;
  }

  hasVirtualCurrencyRate(): boolean {
    return !!this.virtualCurrencyRate;
  }

  isSpecific2FAEnabled( twofaType: AvailableTwoFAOptions ) {
    return this.twoFactorAuthSettings.authOptions.indexOf(twofaType) > -1;
  }

  getActualSettings(): EntitySettingsModel {
    return <EntitySettingsModel>Object.assign({}, this.rawSettings, {
      twoFactorAuthSettings: this.twoFactorAuthSettings
    });
  }

  setEmailTemplate( template: MailTemplatesList ) {
    this.twoFactorAuthSettings.mailTemplates = template;
  }

  setSmsTemplate( template: SmsTemplatesList ) {
    this.twoFactorAuthSettings.smsTemplates = template;
  }

  update2FAMethods( methods ) {
    this.twoFactorAuthSettings.authOptions = Object.keys(methods)
      .filter(( methodName ) => methods[methodName]) as AvailableTwoFAOptions[];
  }

  initTwoFactorSettings() {
    this.twoFactorAuthSettings = createDefaultTwoFASettings();
  }

  private check2FASettingsIntegrity() {
    if (this.twoFactorAuthSettings) {
      if (!this.twoFactorAuthSettings.hasOwnProperty('authOptions')) {
        Object.assign(this.twoFactorAuthSettings, { authOptions: [] });
      }

      if (!this.twoFactorAuthSettings.hasOwnProperty('isAuthEnabled')) {
        Object.assign(this.twoFactorAuthSettings, { isAuthEnabled: false });
      }

      if (!this.twoFactorAuthSettings.hasOwnProperty('smsTemplates')) {
        Object.assign(this.twoFactorAuthSettings, { smsTemplates: { default: createDefaultTwoFASmsTemplate() } });
      }

      if (!this.twoFactorAuthSettings.hasOwnProperty('mailTemplates')) {
        Object.assign(this.twoFactorAuthSettings, { mailTemplates: { default: createDefaultTwoFAEmailTemplate() } });
      }
    } else if (!this.twoFactorAuthSettings) {
      this.initTwoFactorSettings();
    }
  }
}
